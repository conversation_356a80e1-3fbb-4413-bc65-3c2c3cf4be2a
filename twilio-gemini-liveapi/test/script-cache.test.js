import { test } from 'node:test';
import assert from 'node:assert';
import { ScriptCache } from '../src/scripts/script-cache';

test('ScriptCache retrieves items within timeout', () => {
    const cache = new ScriptCache(50);
    cache.set('a', { foo: 'bar' });
    const item = cache.get('a');
    assert.strictEqual(item.foo, 'bar');
});

test('ScriptCache expires items after timeout', async () => {
    const cache = new ScriptCache(10);
    cache.set('b', { foo: 'bar' });
    await new Promise(r => setTimeout(r, 20));
    const item = cache.get('b');
    assert.strictEqual(item, null);
});
