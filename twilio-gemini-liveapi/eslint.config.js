import js from '@eslint/js';
import typescript from '@typescript-eslint/eslint-plugin';
import typescriptParser from '@typescript-eslint/parser';

export default [
    js.configs.recommended,
    {
        files: ['**/*.js'],
        languageOptions: {
            ecmaVersion: 2022,
            sourceType: 'module',
            globals: {
                console: 'readonly',
                process: 'readonly',
                Buffer: 'readonly',
                __dirname: 'readonly',
                __filename: 'readonly',
                global: 'readonly',
                setTimeout: 'readonly',
                clearTimeout: 'readonly',
                setInterval: 'readonly',
                clearInterval: 'readonly'
            }
        },
        rules: {
            // Error prevention
            'no-unused-vars': ['warn', { 'argsIgnorePattern': '^_' }],
            'no-undef': 'error',
            'no-console': 'off', // We use console for logging
            'no-debugger': 'warn',
            
            // Code quality
            'prefer-const': 'warn',
            'no-var': 'error',
            'eqeqeq': ['error', 'always'],
            'curly': ['error', 'all'],
            
            // Style (relaxed for this project)
            'indent': ['warn', 4],
            'quotes': ['warn', 'single', { 'allowTemplateLiterals': true }],
            'semi': ['warn', 'always'],
            'comma-dangle': ['warn', 'never'],
            
            // Audio processing specific
            'no-magic-numbers': 'off', // Audio processing has many magic numbers
            'max-len': ['warn', { 'code': 120 }],
            'complexity': ['warn', 15] // Audio processing can be complex
        },
        ignores: [
            'node_modules/**',
            'audio-debug/**',
            'data/**',
            '*.min.js'
        ]
    },
    {
        files: ['**/*.ts', '**/*.d.ts'],
        languageOptions: {
            parser: typescriptParser,
            ecmaVersion: 2022,
            sourceType: 'module',
            parserOptions: {
                project: './tsconfig.json'
            },
            globals: {
                console: 'readonly',
                process: 'readonly',
                Buffer: 'readonly',
                __dirname: 'readonly',
                __filename: 'readonly',
                global: 'readonly',
                setTimeout: 'readonly',
                clearTimeout: 'readonly',
                setInterval: 'readonly',
                clearInterval: 'readonly',
                NodeJS: 'readonly'
            }
        },
        plugins: {
            '@typescript-eslint': typescript
        },
        rules: {
            ...typescript.configs.recommended.rules,
            // TypeScript specific rules
            '@typescript-eslint/no-unused-vars': ['warn', { 'argsIgnorePattern': '^_' }],
            '@typescript-eslint/no-explicit-any': 'warn',
            '@typescript-eslint/explicit-module-boundary-types': 'off',
            '@typescript-eslint/no-inferrable-types': 'off',
            '@typescript-eslint/ban-ts-comment': 'off',
            
            // General rules
            'no-console': 'off',
            'prefer-const': 'warn',
            'eqeqeq': ['error', 'always'],
            'curly': ['error', 'all'],
            'semi': ['warn', 'always'],
            'quotes': ['warn', 'single', { 'allowTemplateLiterals': true }],
            'comma-dangle': ['warn', 'never'],
            'max-len': ['warn', { 'code': 120 }],
            'complexity': ['warn', 15]
        }
    }
];
