// Simple authentication middleware for Supabase integration
import { authLogger } from '../utils/logger';
import { timingSafeEqual } from 'crypto';
import { FastifyRequest, FastifyReply } from 'fastify';

interface AuthRequest extends FastifyRequest {
  auth?: {
    token: string;
    authenticated: boolean;
  };
}

export async function validateSupaba<PERSON>Auth(request: AuthRequest, reply: FastifyReply): Promise<void> {
  // Skip auth for WebSocket connections
  if (request.headers && request.headers.upgrade === 'websocket') {
    return;
  }

  // Skip auth for Twilio webhooks and health check endpoints
  const skipPaths = [
    '/incoming-call',    // Twilio webhook
    '/call-status',      // Twilio webhook
    '/recording-status', // Twilio webhook
    '/media-stream',     // Twilio WebSocket
    '/health',           // Health check
    '/static',           // Static files
    '/get-campaign-script' // Campaign scripts for frontend
  ];

  if (skipPaths.some(path => request.url.startsWith(path))) {
    return; // Continue without validation
  }
  
  const authHeader = request.headers?.authorization;
  
  if (!authHeader) {
    // Enforce authentication in production
    if (process.env.NODE_ENV === 'production') {
      authLogger.error('No authorization header in production', {
        url: request.url,
        method: request.method,
        ip: request.ip
      });
      reply.code(401).send({ error: 'Authorization required' });
      return;
    }
    // Allow in development and test environments with warning
    if (process.env.NODE_ENV === 'test') {
      authLogger.debug('No authorization header - allowing for test environment');
    } else {
      authLogger.warn('No authorization header - allowing for development only');
    }
    return;
  }

  try {
    // Extract token from "Bearer <token>" format
    const token = authHeader.replace('Bearer ', '');
    
    if (!token || token === 'undefined' || token === 'null') {
      throw new Error('Invalid token format');
    }

    // Enhanced token validation for production security
    if (process.env.NODE_ENV === 'production') {
      // Validate token format (basic check)
      if (token.length < 32) {
        throw new Error('Token too short');
      }

      // Check against a configured API key using timing-safe comparison
      const validApiKey = process.env.API_KEY || process.env.SUPABASE_SERVICE_KEY;
      if (!validApiKey) {
        throw new Error('No API key configured for production');
      }

      // Ensure both tokens are the same length to prevent timing attacks
      if (token.length !== validApiKey.length) {
        throw new Error('Invalid API key');
      }

      // Use timing-safe comparison to prevent timing attacks
      if (!timingSafeEqual(Buffer.from(token), Buffer.from(validApiKey))) {
        throw new Error('Invalid API key');
      }
    }
    
    authLogger.info('Auth validation passed', {
      tokenLength: token.length,
      url: request.url
    });
    
    // Attach user info to request for downstream use
    request.auth = {
      token,
      authenticated: true
    };
    
  } catch (error) {
    authLogger.error('Auth validation failed', error instanceof Error ? error : new Error(String(error)));
    reply.code(401).send({ error: 'Unauthorized' });
  }
}

// Middleware to require auth for specific routes
export function requireAuth(
  request: AuthRequest, 
  reply: FastifyReply, 
  done: (err?: Error) => void
): void {
  validateSupabaseAuth(request, reply)
    .then(() => done())
    .catch(() => {
      reply.code(401).send({ error: 'Authentication required' });
      done();
    });
}